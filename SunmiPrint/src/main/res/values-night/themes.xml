<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.PrinterXSample" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">#FFFFFFFF</item>
        <item name="colorPrimaryVariant">#FFFFFFFF</item>
        <item name="colorOnPrimary">#FF333C4F</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">#FFFF6000</item>
        <item name="colorSecondaryVariant">#FFFF6000</item>
        <item name="colorOnSecondary">@android:color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="colorSurface">#EDEDF0</item>

        <item name="android:actionOverflowButtonStyle">@style/OverflowStyle</item>
    </style>
</resources>