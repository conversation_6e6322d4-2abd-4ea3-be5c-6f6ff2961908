<resources>
    <string name="app_name" translatable="false">PrinterXSample</string>

    <string name="title_info">Printer details</string>
    <string name="title_ticket">Print receipt</string>
    <string name="title_label">Print labels</string>
    <string name="title_file">Print documents</string>
    <string name="title_command">Print command</string>
    <string name="title_cash">Cash drawer control</string>
    <string name="title_lcd">Customer display control</string>

    <string name="tip">This Demo is used to demonstrate and introduce how to use PrinterX SDK to control the printer printing content of Sunmi equipment and the peripheral control related to transactions.</string>
    <string name="tip_info">Displays the printer information connected to the Sunmi device. If there are multiple printers, you can switch to the printer controlled by the current SDK for testing. The built-in printer of Sunmi is controlled by default.</string>
    <string name="tip_cmd">A collection of command transparent transmission interfaces for direct use by developers who have constructed printing content through commands. Currently, two command sets are provided: ESC/POS and TSPL command sets.</string>
    <string name="tip_bluetooth">If the current Sunmi device supports printing, you can also connect and communicate through the built-in virtual Bluetooth: InnerPrinter, and use the command set to send it to the Sunmi virtual Bluetooth printer on the device for printing.</string>

    <string name="text_change">Operate the current printer</string>
    <string name="text_status">State</string>
    <string name="text_name">Name</string>
    <string name="text_type">Type</string>
    <string name="text_paper">Specification</string>
    <string name="text_cash">Cash drawer</string>

    <string name="text_ticket_text">Test text printing</string>
    <string name="text_ticket_texts">Test text in columns printing</string>
    <string name="text_ticket_bar">Test barcode printing</string>
    <string name="text_ticket_qr">Test QR code printing</string>
    <string name="text_ticket_logo">Test image printing</string>
    <string name="text_ticket_line">Test dividing printing</string>
    <string name="text_ticket_all">Complete sample</string>
    <string name="text_ticket_result">Printing with results available</string>

    <string name="text_label_count">Number of prints</string>
    <string name="text_label_test1">Print simple labels</string>
    <string name="text_label_test2">Print product labels</string>
    <string name="text_label_test3">Print brand labels</string>

    <string name="text_cash_switch">Open cash drawer</string>
    <string name="text_cash_status">Get cash drawer status</string>

    <string name="text_lcd_ctrl">Customer display screen control</string>
    <string name="text_lcd_line">Display single line content</string>
    <string name="text_lcd_lines">Display multiple lines of content</string>
    <string name="text_lcd_logo">Display bitmap content</string>
    <string name="text_lcd_digital">Display price content</string>

    <string name="text_cmd_esc">ESC/POS command to print sample</string>
    <string name="text_cmd_tspl">TSPL command print sample</string>
    <string name="text_bluetooth_esc">Virtual Bluetooth printing ESC/POS command</string>
    <string name="text_bluetooth_tspl">Virtual Bluetooth printing TSPL command</string>

    <string name="text_file_url">Specify network address file printing</string>
    <string name="text_file_file">Select local file to print</string>

</resources>