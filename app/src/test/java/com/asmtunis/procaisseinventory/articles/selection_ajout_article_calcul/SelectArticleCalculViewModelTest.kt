package com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul

import android.util.Log
import com.asmtunis.procaisseinventory.MainCoroutineListener
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain


@OptIn(ExperimentalCoroutinesApi::class)
class SelectArticleCalculViewModelTest : StringSpec({
  //  coroutineTestScope = true

    listeners(MainCoroutineListener())


    // Mock the Log class using MockK
  mockkStatic(Log::class)

// Define the mock behavior for the Log.d() method
   every { Log.d(any(), any()) } returns 0

     val proInventoryLocalDb: ProInventoryLocalDb = mockk(relaxed = true)


     val proCaisseLocalDb: ProCaisseLocalDb = mockk(relaxed = true)



     val testScheduler = TestCoroutineScheduler()
    Dispatchers.setMain(StandardTestDispatcher(testScheduler))

     lateinit var selectArticleCalculViewModel: SelectArticleCalculViewModel


     beforeSpec {
         clearMocks(proInventoryLocalDb, proCaisseLocalDb)
         selectArticleCalculViewModel = SelectArticleCalculViewModel(
             proInventoryLocalDb = proInventoryLocalDb,
             proCaisseLocalDb = proCaisseLocalDb
         )
     }



    "addNewLigneSelectedMobilityArtcle qty noDiscount noPromo useSalePrice = false" {
        runTest {
            //  dispatcher.scheduler.advanceUntilIdle()
            // Given
            val article = Article(
                aRTCode = "12345",
                aRTDesignation = "Test Article",
                aRTPrixUnitaireHT = "10.0",
                pvttc = 12.0,
                sARTQte = 100.0,
                aRTTVA = 20.0
            )
            val selectedArticle = SelectedArticle(article = article, quantity = "10")

            selectArticleCalculViewModel.setSelectedArticll(selectedArticle)

            val tva:Tva = Tva(tVACode = "20")

            // When
            selectArticleCalculViewModel.addNewLigneSelectedMobilityArtcle(
                hasPromo = false,
                remise = "0.0",
                operation = Globals.ADD,
                quantiti = "",
                typeOperation = "",
                tva = tva,
                useSalePrice = false
            )

            val result = selectArticleCalculViewModel.selectedArticle



            result.quantity shouldBe "11.0"

            result.prixVente shouldBe "12.0"
            result.prixCaisse shouldBe  "12.0"
            result.mntDiscount.toString() shouldBe "0.0"
            result.lTMtTTC shouldBe   "132.0"
            result.discount shouldBe  ""
            result.lTMtBrutHT shouldBe  "110.0"
            result.mntTva.toString() shouldBe    "22.0"
            result.lTPuHT shouldBe  "10.0"
            result.lTPuTTC shouldBe "12.0"
            result.lTMtNetHT shouldBe "110.0"
        }
    }



    "calculatePricesMobility qty noDiscount noPromo" {
runTest {


        //  dispatcher.scheduler.advanceUntilIdle()
        // Given
        val article = Article(
            aRTCode = "12345",
            aRTDesignation = "Test Article",
            aRTPrixUnitaireHT = "10.0",
            pvttc = 12.0,
            sARTQte = 100.0,
            aRTTVA = 20.0
        )
        val selectedArticle = SelectedArticle(article = article, quantity = "10")

        selectArticleCalculViewModel.setSelectedArticll(selectedArticle)

            val tva:Tva = Tva(tVACode = "20")

        // When
        selectArticleCalculViewModel.calculatePricesMobility(
            hasPromo = false,
            discount = "0.0",
            operation = Globals.ADD,
            qteInput = "",
            typeOperation = "",
            tva = tva
        )

        val result = selectArticleCalculViewModel.selectedArticle



    result.quantity shouldBe "11.0"

    result.prixVente shouldBe "12.0"
    result.prixCaisse shouldBe  "12.0"
    result.mntDiscount.toString() shouldBe "0.0"
    result.lTMtTTC shouldBe   "132.0"
    result.discount shouldBe  ""
    result.lTMtBrutHT shouldBe  "110.0"
    result.mntTva.toString() shouldBe    "22.0"
    result.lTPuHT shouldBe  "10.0"
    result.lTPuTTC shouldBe "12.0"
    result.lTMtNetHT shouldBe "110.0"
    }
    }

    "calculatePricesMobility qty 10.05% Discount noPromo" {
        runTest {


            //  dispatcher.scheduler.advanceUntilIdle()
            // Given
            val article = Article(
                aRTCode = "12345",
                aRTDesignation = "Test Article",
                aRTPrixUnitaireHT = "10.0",
                pvttc = 12.0,
                sARTQte = 100.0,
                aRTTVA = 20.0
            )
            val selectedArticle = SelectedArticle(article = article, quantity = "10", lTPuHT = "10.0")

            selectArticleCalculViewModel.setSelectedArticll(selectedArticle)

            val tva:Tva = Tva(tVACode = "20")

            // When
            selectArticleCalculViewModel.calculatePricesMobility(
                hasPromo = false,
                discount = "10.05",
                // selectedArticle = selectedArticle,
                operation = Globals.NO_OPERATION,
                qteInput = "",
                typeOperation = "remise",
                tva = tva
            )

            val result = selectArticleCalculViewModel.selectedArticle



            result.quantity shouldBe "10"

            result.prixVente shouldBe "12.0"
            result.prixCaisse shouldBe  "10.794"
            result.mntDiscount.toString() shouldBe "12.059999999999995"
            result.lTMtTTC shouldBe  "107.94"
            result.discount shouldBe  "10.05"
            result.lTMtBrutHT shouldBe  "100.0"
            result.mntTva.toString() shouldBe    "17.990000000000002"
            result.lTPuHT shouldBe  "10.0"
            result.lTPuTTC shouldBe "10.794"
            result.lTMtNetHT shouldBe "89.95"
        }
    }








    "calculatePricesInventory qty noDiscount" {
        runTest {


            //  dispatcher.scheduler.advanceUntilIdle()
            // Given
            val article = Article(
                aRTCode = "12345",
                aRTDesignation = "Test Article",
                aRTPrixUnitaireHT = "10.0",
                pvttc = 12.0,
                sARTQte = 100.0,
                aRTTVA = 20.0
            )
            val selectedArticle = SelectedArticle(article = article, quantity = "10")

            selectArticleCalculViewModel.setSelectedArticll(selectedArticle)

            val tva:Tva = Tva(tVACode = "20")

            // When
            selectArticleCalculViewModel.calculatePricesInventory(
                hasPromo = false,
                discount = "0.0",
                // selectedArticle = selectedArticle,
                operation = Globals.ADD,
                qteInput = "",
                typeOperation = "",
                tva = tva
            )

            val result = selectArticleCalculViewModel.selectedArticle



            result.quantity shouldBe "11.0"

            result.prixVente shouldBe "12.0"
            result.prixCaisse shouldBe  "12.0"
            result.mntDiscount.toString() shouldBe "0.0"
            result.lTMtTTC shouldBe   "132.0"
            result.discount shouldBe  ""
            result.lTMtBrutHT shouldBe  "110.0"
            result.mntTva.toString() shouldBe    "22.0"
            result.lTPuHT shouldBe  "10.0"
            result.lTPuTTC shouldBe "12.0"
            result.lTMtNetHT shouldBe "110.0"
        }
    }

    "calculatePricesInventory qty 10.05% Discount noPromo" {
        runTest {


            //  dispatcher.scheduler.advanceUntilIdle()
            // Given
            val article = Article(
                aRTCode = "12345",
                aRTDesignation = "Test Article",
                aRTPrixUnitaireHT = "10.0",
                pvttc = 12.0,
                sARTQte = 100.0,
                aRTTVA = 20.0
            )
            val selectedArticle = SelectedArticle(article = article, quantity = "10", lTPuHT = "10.0")

            selectArticleCalculViewModel.setSelectedArticll(selectedArticle)

            val tva:Tva = Tva(tVACode = "20")

            // When
            selectArticleCalculViewModel.calculatePricesInventory(
                hasPromo = false,
                discount = "10.05",
                // selectedArticle = selectedArticle,
                operation = Globals.NO_OPERATION,
                qteInput = "",
                typeOperation = "remise",
                tva = tva
            )

            val result = selectArticleCalculViewModel.selectedArticle



            result.quantity shouldBe "10"

            result.prixVente shouldBe "12.0"
            result.prixCaisse shouldBe  "10.794"
            result.mntDiscount.toString() shouldBe "10.05"
            result.lTMtTTC shouldBe  "107.94"
            result.discount shouldBe  "10.05"
            result.lTMtBrutHT shouldBe  "100.0"
            result.mntTva.toString() shouldBe    "17.990000000000002"
            result.lTPuHT shouldBe  "10.0"
            result.lTPuTTC shouldBe "10.794"
            result.lTMtNetHT shouldBe "89.95"
        }
    }


})







