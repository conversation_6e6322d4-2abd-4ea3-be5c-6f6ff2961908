package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.dao.TicketDAO
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.ClientCA
import kotlinx.coroutines.flow.Flow

class TicketLocalRepositoryImpl(
    private val ticketDAO: TicketDAO,
) : TicketLocalRepository {

    override fun upsertAll(value: List<Ticket>) = ticketDAO.insertAll(value)

    override fun upsert(value: Ticket) = ticketDAO.insert(value)

    override fun notSynced(): Flow<List<TicketWithFactureAndPayments>?> = ticketDAO.notSynced()

    override fun ticketWithLinesAndPaymentByNumTicket(numTicket: String): Flow<TicketWithFactureAndPayments> =
        ticketDAO.ticketWithLinesAndPaymentByNumTicket(
            numTicket = numTicket,
        )

    override fun deleteAll() = ticketDAO.deleteAll()

    override fun deleteByCodeM(
        code: String,
        exercice: String,
    ) = ticketDAO.deleteByCodeM(
        code = code,
        exercice = exercice,
    )

    override fun updateSyncErrorMsg(
        tikNumTicketM: String,
        errorMsg: String,
    ) = ticketDAO.updateSyncErrorMsg(
        tikNumTicketM = tikNumTicketM,
        errorMsg = errorMsg,
    )

    override fun updateBLNumber(
        tikNumTicket: Int,
        tikNumTicketM: String,
        tikDdm: String,
    ) = ticketDAO.updateBLNumber(
        tikNumTicket = tikNumTicket,
        tikNumTicketM = tikNumTicketM,
        tikDdm = tikDdm,
    )

    override fun updateTicketNumber(
        tikNumBl: String,
        tikNumTicket: Int,
        tikNumTicketM: String,
        tikDdm: String,
    ) = ticketDAO.updateTicketNumber(
        tikNumBl = tikNumBl,
        tikNumTicket = tikNumTicket,
        tikNumTicketM = tikNumTicketM,
        tikDdm = tikDdm,
    )

    override fun updateSessionId(
        tikNumTicketM: String,
        sessionId: String,
    ) = ticketDAO.updateSessionId(
        tikNumTicketM = tikNumTicketM,
        sessionId = sessionId,
    )

    override fun updateTicketStatus(
        tikNumTicketM: String,
        status: String,
    ) = ticketDAO.updateTicketStatus(
        tikNumTicketM = tikNumTicketM,
        status = status,
    )

    override fun getAll(): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>> = ticketDAO.all()

    override fun getNTopClientsBySCaisse(
        idSCaisse: String?,
        number: Int,
    ): Flow<List<ClientCA>> =
        ticketDAO.getNTopClientsBySCaisse(
            idSCaisse = idSCaisse,
            number = number,
        )

    override fun getCA(idSCaisse: String): Flow<Double> = ticketDAO.getCA(idSCaisse = idSCaisse)

    override fun getTicketNumber(idSCaisse: String): Flow<String> = ticketDAO.getTicketNumber(idSCaisse = idSCaisse)

    override fun getMntCredit(caisse: String): Flow<String?> = ticketDAO.getMntCredit(caisse = caisse)

    override fun getByClient(
        codeClient: String,
        sCIdSCaisse: String,
    ): Flow<Map<Ticket, List<LigneTicket>>> =
        ticketDAO.getByClient(
            codeClient = codeClient,
            sCIdSCaisse = sCIdSCaisse,
        )

    override fun getAllFiltred(
        isAsc: Int,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String,
        sortBy: String,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>> =
        ticketDAO.getAllFiltred(
            isAsc = isAsc,
            filterBySessionCaisse = filterBySessionCaisse,
            filterByTicketEtat = filterByTicketEtat,
            filterByTicketSource = filterByTicketSource,
            sortBy = sortBy,
        )

    override fun filterByNumBL(
        searchString: String,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>> =
        ticketDAO.filterByNumBL(
            searchString = searchString,
            //filterBySessionCaisse = filterBySessionCaisse,
            filterByTicketEtat = filterByTicketEtat,
            filterByTicketSource = filterByTicketSource,
            sortBy = sortBy,
            isAsc = isAsc,
        )

    override fun filterByTicketNum(
        searchString: String,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>> =
        ticketDAO.filterByTicketNum(
            searchString = searchString,
            //filterBySessionCaisse = filterBySessionCaisse,
            filterByTicketEtat = filterByTicketEtat,
            filterByTicketSource = filterByTicketSource,
            sortBy = sortBy,
            isAsc = isAsc,
        )

    override fun filterByClient(
        searchString: String,
        filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>> =
        ticketDAO.filterByClient(
            searchString = searchString,
            //filterBySessionCaisse = filterBySessionCaisse,
            filterByTicketEtat = filterByTicketEtat,
            filterByTicketSource = filterByTicketSource,
            sortBy = sortBy,
            isAsc = isAsc,
        )
}
