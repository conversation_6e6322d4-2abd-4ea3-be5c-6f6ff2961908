package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.TICKET_FACTURE_DEJA
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.timbersValueSum
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncBonLivraisonViewModel
@Inject
constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher val mainDispatcher: CoroutineDispatcher,
    val proCaisseRemote: ProCaisseRemote,
    val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    private val dataStoreRepository: DataStoreRepository,
    // app: Application
) : ViewModel() {


    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private var listActifTimberFlow = proCaisseLocalDb.timbre.getActif().distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    var responseAddBatchTicketWithLignesState: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set
    var notSyncAddBatchTicketWithLignesObj : String by mutableStateOf("")
        private set
    var responseFactureTicket: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set

    var ticketsWithLinesAndPaymentsNotSync: List<TicketWithFactureAndPayments> by mutableStateOf(emptyList())
        private set
    init {
        getNotSyncBonLivraison()
    }


    var listActifTimber by  mutableStateOf(emptyList<Timbre>())
        private set


    private fun getNotSyncBonLivraison() {
        viewModelScope.launch {
            val bonLivraisonNotSyncFlow = proCaisseLocalDb.bonLivraison.notSynced().distinctUntilChanged()


            combine(networkFlow, bonLivraisonNotSyncFlow, autoSyncFlow, listActifTimberFlow) { isConnected, inventaireNotSyncList, autoSync, listActifTimberFlow ->
                listActifTimber = listActifTimberFlow.ifEmpty { emptyList() }
                connected = isConnected
                autoSyncState = autoSync
                inventaireNotSyncList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    ticketsWithLinesAndPaymentsNotSync = emptyList()
                    return@collect
                }
                ticketsWithLinesAndPaymentsNotSync = it
                if(connected && autoSyncState) syncBonLivraison()
            }
        }
    }


    private fun setFactureTimbreAndRevImp() {

        val timberValue = timbersValueSum(listActifTimber)

        ticketsWithLinesAndPaymentsNotSync.forEach {ticketsWithLinesAndPayments->
            val client = ticketsWithLinesAndPayments.client
            val ticket = ticketsWithLinesAndPayments.ticket

            ticketsWithLinesAndPayments.ticket = ticket?.copy(
                tIKTimbre =  if (stringToDouble(client?.cLITimbre?:"0") == 0.0) "0" else listActifTimber.first().tIMBCode
            )
        }

    }
    fun syncBonLivraison(selectedTicket: TicketWithFactureAndPayments = TicketWithFactureAndPayments()) {
        viewModelScope.launch(dispatcherIO) {
            val autoFacture = proCaisseLocalDb.dataStore.getBoolean(AUTO_FACTURE_AUTHORISATION).first()

            if(autoFacture) {
                setFactureTimbreAndRevImp()
            }

            // Récupérer l'ID de la session de caisse active
            val activeSessionId = getActiveSessionId()
            Log.d("SyncBonLivraisonViewModel", "Synchronisation avec la session de caisse active: $activeSessionId")

            // Ajouter l'ID de session aux tickets à synchroniser
            if (!activeSessionId.isNullOrEmpty()) {
                if (selectedTicket != TicketWithFactureAndPayments()) {
                    // Mettre à jour le ticket sélectionné
                    selectedTicket.ticket?.let { ticket ->
                        val updatedTicket = ticket.copy(tIKIdSCaisse = activeSessionId.toString())
                        selectedTicket.ticket = updatedTicket
                        Log.d("SyncBonLivraisonViewModel", "Ticket sélectionné ${ticket.tIKNumTicketM} associé à la session $activeSessionId")
                    }
                } else {
                    // Mettre à jour tous les tickets non synchronisés
                    ticketsWithLinesAndPaymentsNotSync.forEach { ticketWithLines ->
                        ticketWithLines.ticket?.let { ticket ->
                            val updatedTicket = ticket.copy(tIKIdSCaisse = activeSessionId.toString())
                            ticketWithLines.ticket = updatedTicket
                            Log.d("SyncBonLivraisonViewModel", "Ticket ${ticket.tIKNumTicketM} associé à la session $activeSessionId")
                        }
                    }
                }
                Log.d("SyncBonLivraisonViewModel", "Tous les tickets ont été associés à la session active $activeSessionId")
            } else {
                Log.w("SyncBonLivraisonViewModel", "Aucune session active trouvée - les tickets ne seront pas associés à une session")
            }

            val baseConfigObj =
                GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(if(selectedTicket != TicketWithFactureAndPayments()) listOf(selectedTicket)  else ticketsWithLinesAndPaymentsNotSync),
                )


            notSyncAddBatchTicketWithLignesObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.ticket.addBatchTicketWithLignesTicketAndPayment(
                baseConfig = notSyncAddBatchTicketWithLignesObj,
                autoFacture = autoFacture,
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = result.data, loading = false, error = null)

                        for (i in result.data!!.indices) {

                            val ticketUpdate = result.data[i]
                            Log.d("SyncBonLivraisonViewModel", "Code de réponse: ${ticketUpdate.code}, Message: ${ticketUpdate.message}")

                            // Gérer les différents codes de réponse
                            when (ticketUpdate.code) {
                                "10200", "10201" -> {
                                    // Succès ou mise à jour réussie
                                    Log.d("SyncBonLivraisonViewModel", "Synchronisation réussie pour ticket ${ticketUpdate.tIKNumTicketM} - Code: ${ticketUpdate.code}")
                                    updateLocalData(ticketUpdate = ticketUpdate, insertFacture = autoFacture)
                                }
                                "10302" -> {
                                    // Ticket n'existe pas - le créer et l'ajouter au journal de caisse
                                    Log.d("SyncBonLivraisonViewModel", "Code 10302 - Ticket ${ticketUpdate.tIKNumTicketM} n'existe pas sur le serveur, création et ajout au journal de caisse")
                                    // Ce code indique que le ticket a été créé avec succès sur le serveur
                                    // Il faut maintenant le transformer en ticket du journal de caisse
                                    updateLocalData(ticketUpdate = ticketUpdate, insertFacture = autoFacture)
                                    Log.d("SyncBonLivraisonViewModel", "Ticket ${ticketUpdate.tIKNumTicketM} créé et ajouté au journal de caisse avec succès")
                                }
                                else -> {
                                    // Autres erreurs
                                    Log.e("SyncBonLivraisonViewModel", "Erreur de synchronisation pour ticket ${ticketUpdate.tIKNumTicketM} - Code: ${ticketUpdate.code}, Message: ${ticketUpdate.message}")
                                    responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = ticketUpdate.message)

                                    // Mettre à jour le message d'erreur dans la base de données locale
                                    ticketUpdate.tIKNumTicketM?.let { tikNumTicketM ->
                                        proCaisseLocalDb.bonLivraison.updateSyncErrorMsg(
                                            tikNumTicketM = tikNumTicketM,
                                            errorMsg = ticketUpdate.message ?: "UnkownError",
                                        )
                                    }
                                }
                            }
                        }
                    }

                    is DataResult.Loading -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedTicket.ticket?.tIKNumTicket)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
    fun facturerBL(ticketWithFactureAndPayments: TicketWithFactureAndPayments) {

        ticketsWithLinesAndPaymentsNotSync = emptyList()
        ticketsWithLinesAndPaymentsNotSync = listOf(ticketWithFactureAndPayments)
        setFactureTimbreAndRevImp()

        viewModelScope.launch(dispatcherIO) {
            // Check network connectivity before attempting synchronization
            if (!connected) {
                responseFactureTicket = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "No network connection available. Please check your internet connection and try again."
                )
                return@launch
            }
            val baseConfigObj =
                GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(ticketsWithLinesAndPaymentsNotSync),
                )

            proCaisseRemote.ticket.addBatchFactureWithLines(baseConfig = Json.encodeToString(baseConfigObj)).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        responseFactureTicket = RemoteResponseState(data = result.data, loading = false, error = null)
                        ticketsWithLinesAndPaymentsNotSync = emptyList()
                        for (i in result.data!!.indices) {
                            val ticketUpdate = result.data[i]
                            if (!ticketUpdate.code.equals("10200") && !ticketUpdate.code.equals("10201") && !ticketUpdate.code.equals("10304")) {
                                // TODO HANDLE OTHER CASES HERE
                                return@onEach
                            }

                            updateLocalData(ticketUpdate = ticketUpdate, insertFacture = true)
                        }
                    }

                    is DataResult.Loading -> {
                        responseFactureTicket = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        ticketsWithLinesAndPaymentsNotSync = emptyList()
                        responseFactureTicket = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    private fun updateClientSold(
        cLICode: String,
        ticketUpdate: TicketUpdate,
    )  {
        viewModelScope.launch(dispatcherIO) {
            proCaisseLocalDb.clients.updateMoneyClient(
                codeClt = cLICode,
                soldClient = ticketUpdate.soldeClient?: "",
                cliCredit = ticketUpdate.credit?: "",
                cliDebit = ticketUpdate.debit?: "",
            )
        }
    }




    private fun updateLocalData(ticketUpdate: TicketUpdate, insertFacture: Boolean) {
        val ddm = getCurrentDateTime()

        if(insertFacture) {
            ticketUpdate.facture?.let { proCaisseLocalDb.facture.upsert(it) }
        }
        proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
            codeM = ticketUpdate.tIKNumTicketM?: "",
            newCode = ticketUpdate.tIKNumTicket,
            exercice = ticketUpdate.tIKExerc?: "",
            carnet = ticketUpdate.tIKIdCarnet?: "",
        )
        if (ticketUpdate.tIKNumeroBL == null) {
            // Auto facture == true

            proCaisseLocalDb.bonLivraison.updateBLNumber(
                tikNumTicket = ticketUpdate.tIKNumTicket,
                tikNumTicketM = ticketUpdate.tIKNumTicketM?: "",
                tikDdm = ddm,
            )
        } else {
            // Auto facture == false
            proCaisseLocalDb.bonLivraison.updateTicketNumber(
                tikNumBl = ticketUpdate.tIKNumeroBL?: "",
                tikNumTicket = ticketUpdate.tIKNumTicket,
                tikNumTicketM = ticketUpdate.tIKNumTicketM!!,
                tikDdm = ddm,
            )
        }

        if (ticketUpdate.observation != null) {
            if (!ticketUpdate.observation.equals("")) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = ticketUpdate.observation?: "",
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        if (ticketUpdate.message != null) {
            if (ticketUpdate.message.equals(TICKET_FACTURE_DEJA)) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = ticketUpdate.message?: "",
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        updateClientSold(
            cLICode = ticketUpdate.codeClient?: "",
            ticketUpdate = ticketUpdate,
        )

        // TODO ONLE EXC WHEN IS NOT CREDIT
        proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        proCaisseLocalDb.ticketResto.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        // Le statut JOURNAL_CAISSE est maintenant défini automatiquement par updateBLNumber() et updateTicketNumber()
        Log.d("SyncBonLivraisonViewModel", "Ticket ${ticketUpdate.tIKNumTicketM} transformé en journal de caisse avec succès")
    }

    /**
     * Retrieves BLs with error messages and attempts to resync them individually
     * This provides a retry mechanism for previously failed synchronizations
     */
    fun retryFailedSync() {
        viewModelScope.launch(dispatcherIO) {
            // Check network connectivity before attempting retry
            if (!connected) {
                responseAddBatchTicketWithLignesState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "No network connection available. Please check your internet connection and try again."
                )
                return@launch
            }

            // Get BLs with error messages - use the notSynced method which is available
            val failedBLs = proCaisseLocalDb.bonLivraison.notSynced().first()

            if (failedBLs == null || failedBLs.isEmpty()) {
                responseAddBatchTicketWithLignesState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "No failed BLs found to retry"
                )
                return@launch
            }

            println("Retrying sync for ${failedBLs.size} failed BLs")

            // Process each failed BL individually
            failedBLs.forEach { (ticket, ligneTickets) ->
                // Get client information
                val client = proCaisseLocalDb.clients.getOneByCode(ticket?.tIKCodClt ?: "").first()

                // Get payment information
                val reglementCaisses = proCaisseLocalDb.reglementCaisse.getByTicketM(ticket?.tIKNumTicketM ?: "").first()
                val chequeCaisses = proCaisseLocalDb.chequeCaisse.getByReglementM(ticket?.tIKNumTicketM ?: "").first()
                val ticketRestos = proCaisseLocalDb.ticketResto.getAll().first().filter { it.tRAITNUM_M == ticket?.tIKNumTicketM }

                // Create a TicketWithFactureAndPayments object for the failed BL
                val ticketWithFactureAndPayments = TicketWithFactureAndPayments(
                    ticket = ticket,
                    ligneTicket = ligneTickets,
                    client = client,
                    cheques = chequeCaisses,
                    traites = ticketRestos,
                    reglement = reglementCaisses
                )

                // Attempt to sync this specific BL
                println("Retrying sync for BL: ${ticket?.tIKNumTicketM ?: "Unknown"}")
                syncBonLivraison(ticketWithFactureAndPayments)
            }
        }
    }
    private suspend fun getActiveSessionId(): String? {
        return try {
            // Get the active session from the database
            val activeSession = proCaisseLocalDb.sessionCaisse.getActiveSession().first()
            activeSession?.sCIdSCaisse
        } catch (e: Exception) {
            Log.e("SyncBonLivraisonViewModel", "Error retrieving active session: ${e.message}")
            null
        }
    }}
